using System.Windows.Controls;
using DriverManagementSystem.ViewModels;

namespace DriverManagementSystem.Views
{
    /// <summary>
    /// Interaction logic for AssignmentView.xaml
    /// </summary>
    public partial class AssignmentView : UserControl
    {
        public AssignmentView()
        {
            InitializeComponent();
            // إنشاء ViewModel افتراضي فقط إذا لم يكن هناك DataContext
            if (DataContext == null)
            {
                DataContext = new ReportViewModel();
            }
        }

        /// <summary>
        /// Constructor مع DataContext محدد مسبقاً
        /// </summary>
        public AssignmentView(ReportViewModel viewModel)
        {
            InitializeComponent();
            DataContext = viewModel;
        }
    }
}
