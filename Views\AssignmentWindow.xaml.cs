using System;
using System.Windows;
using DriverManagementSystem.Models;
using DriverManagementSystem.ViewModels;

namespace DriverManagementSystem.Views
{
    /// <summary>
    /// Interaction logic for AssignmentWindow.xaml
    /// </summary>
    public partial class AssignmentWindow : Window
    {
        private readonly FieldVisit _selectedVisit;

        public AssignmentWindow(FieldVisit selectedVisit)
        {
            InitializeComponent();
            _selectedVisit = selectedVisit;

            // تحديث العنوان
            Title = $"التكليف - زيارة {selectedVisit?.VisitNumber ?? "001"}";

            // إنشاء وربط ViewModel للمحتوى
            var reportViewModel = new ReportViewModel();
            reportViewModel.SelectedVisit = selectedVisit;

            // ربط ViewModel بالنافذة والمحتوى
            DataContext = this;
            AssignmentContent.DataContext = reportViewModel;

            // تحميل البيانات
            if (selectedVisit != null)
            {
                _ = reportViewModel.LoadDriversAndPricesData(selectedVisit);
            }
        }

        public FieldVisit SelectedVisit => _selectedVisit;

        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // طباعة محتوى التكليف
                var printDialog = new System.Windows.Controls.PrintDialog();
                if (printDialog.ShowDialog() == true)
                {
                    printDialog.PrintVisual(AssignmentContent, "التكليف");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة التكليف: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
