<UserControl x:Class="DriverManagementSystem.Views.AssignmentView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:DriverManagementSystem.Views"
             FlowDirection="RightToLeft"
             Background="White">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../Styles/PrintStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <!-- Converter للتحقق من وجود الصور -->
            <BooleanToVisibilityConverter x:Key="BoolToVisibilityConverter"/>

            <!-- Converter مخصص للتحقق من وجود النص/الصورة -->
            <local:NullToVisibilityConverter x:Key="NullToVisibilityConverter"/>

            <!-- Converter للتحقق من النص الفارغ -->
            <local:EmptyStringToVisibilityConverter x:Key="EmptyStringToVisibilityConverter"/>

        </ResourceDictionary>
    </UserControl.Resources>

    <!-- Container الرئيسي -->
    <StackPanel Background="White" Margin="20" FlowDirection="LeftToRight">
        <!-- رقم الزيارة فقط -->
        <Border Background="#E3F2FD" BorderBrush="#2196F3" BorderThickness="2"
                Padding="20" Margin="0,50,0,0" HorizontalAlignment="Center">
            <StackPanel HorizontalAlignment="Center">
                <TextBlock Text="رقم الزيارة" FontSize="16" FontWeight="Bold"
                           HorizontalAlignment="Center" TextAlignment="Center" Margin="0,0,0,10"/>
                <TextBlock Text="{Binding ReportData.VisitNumber, FallbackValue='911-1300'}"
                           FontSize="24" FontWeight="Bold"
                           HorizontalAlignment="Center" TextAlignment="Center"/>
            </StackPanel>
        </Border>

    </StackPanel>
</UserControl>
