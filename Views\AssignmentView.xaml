<UserControl x:Class="DriverManagementSystem.Views.AssignmentView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:DriverManagementSystem.Views"
             FlowDirection="RightToLeft"
             Background="White">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../Styles/PrintStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <!-- Converter للتحقق من وجود الصور -->
            <BooleanToVisibilityConverter x:Key="BoolToVisibilityConverter"/>

            <!-- Converter مخصص للتحقق من وجود النص/الصورة -->
            <local:NullToVisibilityConverter x:Key="NullToVisibilityConverter"/>

            <!-- Converter للتحقق من النص الفارغ -->
            <local:EmptyStringToVisibilityConverter x:Key="EmptyStringToVisibilityConverter"/>

        </ResourceDictionary>
    </UserControl.Resources>

    <!-- الإطار الخارجي الأسود بحجم A4 مصغر -->
    <Border BorderBrush="Black" BorderThickness="3" Margin="10" Background="White"
            Width="600" Height="850" HorizontalAlignment="Center" VerticalAlignment="Top">

        <!-- Container الرئيسي -->
        <StackPanel Background="White" Margin="20" FlowDirection="RightToLeft">

            <!-- الهيدر -->
            <Border Background="#F0F0F0" BorderBrush="#CCCCCC" BorderThickness="0,0,0,1"
                    Padding="10" Margin="0,0,0,20">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- النصوص العربية -->
                    <StackPanel Grid.Column="0" VerticalAlignment="Center" HorizontalAlignment="Right"
                                FlowDirection="RightToLeft">
                        <TextBlock Text="الجمهورية اليمنية"
                                   FontSize="12" FontWeight="Bold"
                                   Foreground="#666666" Margin="0,2"
                                   TextAlignment="Right"/>
                        <TextBlock Text="رئاسة مجلس الوزراء"
                                   FontSize="11"
                                   Foreground="#666666" Margin="0,1"
                                   TextAlignment="Right"/>
                        <TextBlock Text="الصندوق الاجتماعي للتنمية"
                                   FontSize="10"
                                   Foreground="#666666" Margin="0,1"
                                   TextAlignment="Right"/>
                        <TextBlock Text="فرع ذمار البيضاء"
                                   FontSize="10"
                                   Foreground="#666666" Margin="0,1"
                                   TextAlignment="Right"/>
                    </StackPanel>

                    <!-- النصوص الإنجليزية -->
                    <StackPanel Grid.Column="1" VerticalAlignment="Center" HorizontalAlignment="Center">
                        <TextBlock Text="Social Fund For Development"
                                   FontSize="12" FontWeight="Bold"
                                   Foreground="#666666" Margin="0,2"
                                   TextAlignment="Center"
                                   FlowDirection="LeftToRight"/>
                        <TextBlock Text="Republic OF YEMEN"
                                   FontSize="11"
                                   Foreground="#666666" Margin="0,1"
                                   TextAlignment="Center"
                                   FlowDirection="LeftToRight"/>
                        <TextBlock Text="Presidency of Council of Ministers"
                                   FontSize="10"
                                   Foreground="#666666" Margin="0,1"
                                   TextAlignment="Center"
                                   FlowDirection="LeftToRight"/>
                        <TextBlock Text="Dhamar &amp;Albidaa Branch"
                                   FontSize="10"
                                   Foreground="#666666" Margin="0,1"
                                   TextAlignment="Center"
                                   FlowDirection="LeftToRight"/>
                    </StackPanel>

                    <!-- الشعار -->
                    <Image Grid.Column="2" Source="../icons/sfd.png"
                           Width="60" Height="60"
                           VerticalAlignment="Center"
                           HorizontalAlignment="Left"
                           Margin="20,0,10,0"/>
                </Grid>
            </Border>

            <!-- رقم الزيارة فقط -->
            <Border Background="#E3F2FD" BorderBrush="#2196F3" BorderThickness="2"
                    Padding="20" Margin="0,50,0,0" HorizontalAlignment="Center">
                <StackPanel HorizontalAlignment="Center">
                    <TextBlock Text="رقم الزيارة" FontSize="16" FontWeight="Bold"
                               HorizontalAlignment="Center" TextAlignment="Center" Margin="0,0,0,10"/>
                    <TextBlock Text="{Binding ReportData.VisitNumber, FallbackValue='911-1300'}"
                               FontSize="24" FontWeight="Bold"
                               HorizontalAlignment="Center" TextAlignment="Center"/>
                </StackPanel>
            </Border>

        </StackPanel>
    </Border>
</UserControl>
